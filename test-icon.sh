#!/bin/bash

echo "=== AppImage 图标测试 ==="
echo ""

# 检查AppImage文件
if [ ! -f "MiniTool-1.0.0-x86_64.AppImage" ]; then
    echo "❌ AppImage文件不存在"
    exit 1
fi

echo "1. 检查AppImage内容..."
./MiniTool-1.0.0-x86_64.AppImage --appimage-extract >/dev/null 2>&1

if [ -d "squashfs-root" ]; then
    echo "✅ AppImage提取成功"
    
    echo ""
    echo "2. 检查图标文件..."
    
    # 检查根目录图标
    if [ -f "squashfs-root/mini_tool.png" ]; then
        echo "✅ 根目录图标: squashfs-root/mini_tool.png"
        ls -la squashfs-root/mini_tool.png
    else
        echo "❌ 根目录图标缺失"
    fi
    
    # 检查.DirIcon
    if [ -L "squashfs-root/.DirIcon" ]; then
        echo "✅ .DirIcon符号链接存在"
        ls -la squashfs-root/.DirIcon
    else
        echo "❌ .DirIcon符号链接缺失"
    fi
    
    # 检查桌面文件
    if [ -f "squashfs-root/MiniTool.desktop" ]; then
        echo "✅ 桌面文件存在"
        echo "内容:"
        cat squashfs-root/MiniTool.desktop
    else
        echo "❌ 桌面文件缺失"
    fi
    
    echo ""
    echo "3. 检查图标目录..."
    if [ -d "squashfs-root/usr/share/icons/hicolor" ]; then
        echo "✅ 图标目录存在"
        find squashfs-root/usr/share/icons/hicolor -name "mini_tool.png" | head -5
    else
        echo "❌ 图标目录缺失"
    fi
    
    echo ""
    echo "4. 图标文件信息..."
    if [ -f "squashfs-root/mini_tool.png" ]; then
        file squashfs-root/mini_tool.png
        echo "大小: $(du -h squashfs-root/mini_tool.png | cut -f1)"
    fi
    
    # 清理
    rm -rf squashfs-root
    
    echo ""
    echo "5. 建议..."
    echo "如果图标仍不显示，可能的原因："
    echo "  • 窗口管理器不支持AppImage图标"
    echo "  • 需要集成AppImage到系统菜单"
    echo "  • 图标缓存需要更新"
    echo ""
    echo "解决方案："
    echo "  1. 使用AppImageLauncher集成到系统"
    echo "  2. 手动更新图标缓存: sudo gtk-update-icon-cache -f /usr/share/icons/hicolor/"
    echo "  3. 重启桌面环境"
    
else
    echo "❌ 无法提取AppImage内容"
fi
